const Koa = require('koa')
const cors = require('@koa/cors') // 跨域处理
const static = require('koa-static') // 静态资源服务
const path = require('path')
const fs = require('fs')
const http2 = require('http2')
const { koaBody } = require('koa-body') //参数解析
const useRouter = require('./src/router') //路由注册
const VerifyMiddleware = require('./src/middleware/verify.middleware')

const app = new Koa()
app.useRouter = useRouter
app.use(cors())
app.use(koaBody())
app.use(
  static('./updates', {
    setHeaders: (ctx, filePath) => {
      const filename = path.basename(filePath)
      ctx.setHeader('Cache-Control', 'public, max-age=2678400000')
      // ctx.setHeader('Content-Disposition', `attachment; filename="${filename}"`)
    }
  })
) // 静态资源服务
app.use(VerifyMiddleware.verifyAuth) // 身份验证
app.useRouter(app).then((res) => {
  app.use(async (ctx, next) => {
    await next()
    if (ctx.status === 404) {
      ctx.body = { code: 404, message: '未查询到资源' }
    }
  })
})
// HTTP/2 配置示例（需要证书文件）
const enableHTTP2 = process.env.ENABLE_HTTP2 === 'true'

if (enableHTTP2) {
  // HTTP/2 需要 HTTPS，必须提供证书和密钥
  const options = {
    // 证书和密钥文件路径（需要根据实际情况调整）
    key: fs.readFileSync(process.env.SSL_KEY_PATH || './ssl/private-key.pem'),
    cert: fs.readFileSync(process.env.SSL_CERT_PATH || './ssl/certificate.pem'),
    // CA 证书（可选，用于证书链验证）
    // ca: fs.readFileSync('./ssl/ca-certificate.pem'),

    // HTTP/2 特定配置
    allowHTTP1: true, // 允许 HTTP/1.1 回退
    settings: {
      enablePush: false, // 禁用服务器推送（可选）
      maxConcurrentStreams: 100, // 最大并发流数量
      maxHeaderListSize: 8192 // 最大头部列表大小
    }
  }

  // 创建 HTTP/2 安全服务器
  const server = http2.createSecureServer(options, app.callback())

  server.listen(3001, () => {
    console.log('HTTP/2 服务器运行在 https://localhost:3001')
  })

  // 错误处理
  server.on('error', (err) => {
    console.error('HTTP/2 服务器错误:', err)
  })
} else {
  // 标准 HTTP/1.1 服务器
  app.listen(3001, () => {
    console.log('HTTP/1.1 服务器运行在 http://localhost:3001')
  })
}
