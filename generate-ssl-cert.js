#!/usr/bin/env node

/**
 * 生成自签名 SSL 证书用于开发环境
 * 注意：生产环境请使用 CA 签发的证书
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

const sslDir = path.join(__dirname, 'ssl')

// 创建 ssl 目录
if (!fs.existsSync(sslDir)) {
  fs.mkdirSync(sslDir, { recursive: true })
  console.log('✅ 创建 ssl 目录')
}

try {
  // 检查 openssl 是否可用
  execSync('openssl version', { stdio: 'ignore' })
  
  console.log('🔧 正在生成自签名证书...')
  
  // 生成私钥
  execSync(`openssl genrsa -out ${path.join(sslDir, 'private-key.pem')} 2048`, { stdio: 'inherit' })
  console.log('✅ 私钥生成完成')
  
  // 生成证书签名请求
  const csrConfig = `
[req]
distinguished_name = req_distinguished_name
req_extensions = v3_req
prompt = no

[req_distinguished_name]
C = CN
ST = Guangdong
L = Shenzhen
O = Development
OU = IT Department
CN = localhost

[v3_req]
keyUsage = keyEncipherment, dataEncipherment
extendedKeyUsage = serverAuth
subjectAltName = @alt_names

[alt_names]
DNS.1 = localhost
DNS.2 = *.localhost
IP.1 = 127.0.0.1
IP.2 = ::1
`
  
  fs.writeFileSync(path.join(sslDir, 'csr.conf'), csrConfig)
  
  // 生成证书
  execSync(`openssl req -new -x509 -key ${path.join(sslDir, 'private-key.pem')} -out ${path.join(sslDir, 'certificate.pem')} -days 365 -config ${path.join(sslDir, 'csr.conf')} -extensions v3_req`, { stdio: 'inherit' })
  console.log('✅ 证书生成完成')
  
  // 清理临时文件
  fs.unlinkSync(path.join(sslDir, 'csr.conf'))
  
  console.log('\n🎉 SSL 证书生成成功！')
  console.log('📁 证书文件位置:')
  console.log(`   私钥: ${path.join(sslDir, 'private-key.pem')}`)
  console.log(`   证书: ${path.join(sslDir, 'certificate.pem')}`)
  console.log('\n⚠️  注意: 这是自签名证书，仅用于开发环境')
  console.log('   浏览器会显示安全警告，这是正常的')
  console.log('   生产环境请使用 CA 签发的证书')
  
} catch (error) {
  console.error('❌ 证书生成失败:', error.message)
  console.log('\n💡 解决方案:')
  console.log('1. 安装 OpenSSL:')
  console.log('   - Windows: 下载并安装 OpenSSL 或使用 Git Bash')
  console.log('   - macOS: brew install openssl')
  console.log('   - Linux: sudo apt-get install openssl')
  console.log('2. 或者使用在线工具生成证书')
  console.log('3. 或者使用 mkcert 工具: https://github.com/FiloSottile/mkcert')
}
