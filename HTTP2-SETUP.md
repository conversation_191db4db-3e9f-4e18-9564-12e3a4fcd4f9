# Koa HTTP/2 配置指南

## 证书和密钥的要求与限制

### 1. 基本要求

HTTP/2 **强制要求 HTTPS**，因此必须提供：
- SSL/TLS 证书文件 (`.crt` 或 `.pem` 格式)
- 私钥文件 (`.key` 或 `.pem` 格式)

### 2. 支持的证书格式

- **PEM 格式** (推荐) - Base64 编码的文本格式
- **DER 格式** - 二进制格式
- **PKCS#12 格式** - 需要先转换为 PEM 格式

### 3. 支持的密钥算法

- **RSA**: 2048位或更高 (推荐4096位)
- **ECDSA**: P-256, P-384, P-521
- **Ed25519**: Node.js 15.6.0+ 支持

### 4. 证书类型

- **自签名证书**: 开发环境可用，浏览器会显示安全警告
- **CA 签发证书**: 生产环境推荐
- **Let's Encrypt**: 免费的 CA 证书
- **通配符证书**: 支持多子域名

## 快速开始

### 步骤 1: 生成开发证书

```bash
# 运行证书生成脚本
node generate-ssl-cert.js

# 或者使用 mkcert (推荐)
npm install -g mkcert
mkcert -install
mkcert localhost 127.0.0.1 ::1
```

### 步骤 2: 配置环境变量

复制 `.env.example` 为 `.env` 并配置：

```env
ENABLE_HTTP2=true
SSL_KEY_PATH=./ssl/private-key.pem
SSL_CERT_PATH=./ssl/certificate.pem
```

### 步骤 3: 启动服务器

```bash
npm start
```

## 生产环境配置

### 使用 Let's Encrypt 证书

```bash
# 安装 certbot
sudo apt-get install certbot

# 获取证书
sudo certbot certonly --standalone -d yourdomain.com

# 证书路径通常在
# /etc/letsencrypt/live/yourdomain.com/privkey.pem
# /etc/letsencrypt/live/yourdomain.com/fullchain.pem
```

### 环境变量配置

```env
ENABLE_HTTP2=true
SSL_KEY_PATH=/etc/letsencrypt/live/yourdomain.com/privkey.pem
SSL_CERT_PATH=/etc/letsencrypt/live/yourdomain.com/fullchain.pem
```

## 常见问题

### 1. 证书格式错误

```javascript
// 错误: 使用了错误的证书格式
Error: error:0909006C:PEM routines:get_name:no start line

// 解决: 确保证书是 PEM 格式，以 -----BEGIN 开头
```

### 2. 私钥不匹配

```javascript
// 错误: 私钥与证书不匹配
Error: error:0B080074:x509 certificate routines:X509_check_private_key:key values mismatch

// 解决: 确保私钥和证书是配对生成的
```

### 3. 文件权限问题

```bash
# 设置正确的文件权限
chmod 600 ssl/private-key.pem
chmod 644 ssl/certificate.pem
```

### 4. 浏览器安全警告

对于自签名证书，浏览器会显示安全警告：
- Chrome: 点击 "高级" -> "继续前往 localhost (不安全)"
- Firefox: 点击 "高级" -> "接受风险并继续"

## 性能优化建议

### 1. 启用 HTTP/2 推送 (可选)

```javascript
settings: {
  enablePush: true, // 启用服务器推送
}
```

### 2. 调整并发流数量

```javascript
settings: {
  maxConcurrentStreams: 200, // 根据服务器性能调整
}
```

### 3. 启用 GZIP 压缩

```javascript
const compress = require('koa-compress')
app.use(compress())
```

## 安全注意事项

1. **生产环境不要使用自签名证书**
2. **定期更新证书** (Let's Encrypt 证书90天过期)
3. **使用强密钥算法** (RSA 2048位以上)
4. **保护私钥文件** (设置正确的文件权限)
5. **启用 HSTS** (HTTP Strict Transport Security)

## 证书更新自动化

```bash
# 创建证书更新脚本
#!/bin/bash
certbot renew --quiet
systemctl reload your-app-service
```

## 监控和日志

```javascript
server.on('error', (err) => {
  console.error('HTTP/2 服务器错误:', err)
  // 发送到日志系统或监控服务
})

server.on('stream', (stream, headers) => {
  console.log('新的 HTTP/2 流:', headers[':path'])
})
```
